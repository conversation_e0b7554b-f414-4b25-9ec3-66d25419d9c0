# api/plant_routes.py
import os
from flask import request, jsonify
from werkzeug.utils import secure_filename
from app import app
from ai.plant_agent import PlantAgent
from utils.missing_plants import get_missing_plants

# Configure upload folder
UPLOAD_FOLDER = 'static/uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/api/identify/text', methods=['POST'])
def api_identify_text():
    """API endpoint for identifying plants by name"""
    data = request.json
    
    if not data or 'plant_name' not in data:
        return jsonify({"error": "No plant name provided"}), 400
    
    plant_name = data['plant_name']
    print(f"searching for {plant_name}")
    # Initialize plant agent
    agent = PlantAgent()
    
    # Query by name
    result = agent.query_by_name(plant_name)
    
    return jsonify(result)

@app.route('/api/identify/image', methods=['POST'])
def api_identify_image():
    """API endpoint for plant identification from image"""
    # Check if image was uploaded
    if 'image' not in request.files:
        return jsonify({"error": "No image uploaded"}), 400
    
    file = request.files['image']
    if file.filename == '':
        return jsonify({"error": "No image selected"}), 400
    
    if file and allowed_file(file.filename):
        # Save the uploaded file
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)
        
        # Initialize plant agent
        agent = PlantAgent()
        
        # Process the image
        result = agent.identify_from_image(file_path)
        
        if result.get('success'):
            # Return identification results
            return jsonify({
                "success": True,
                "image_path": f"/static/uploads/{filename}",
                "result": result.get('result')
            })
        else:
            return jsonify({
                "success": False,
                "error": result.get('error', "Could not identify plant"),
                "details": result.get('details', "Unknown error")
            }), 404
    
    return jsonify({"error": "Invalid file type"}), 400

@app.route('/api/chat', methods=['POST'])
def api_chat():
    """API endpoint for conversational plant identification"""
    data = request.json
    
    if not data or 'message' not in data:
        return jsonify({"error": "No message provided"}), 400
    
    user_message = data['message']
    conversation_id = data.get('conversation_id', 'default')
    
    # Initialize plant agent if not already done
    if not hasattr(app, 'plant_agents'):
        app.plant_agents = {}
    
    # Create or retrieve agent for this conversation
    if conversation_id not in app.plant_agents:
        plant_agent = PlantAgent()
        app.plant_agents[conversation_id] = plant_agent.create_conversational_agent()
    
    # Process the message
    response = app.plant_agents[conversation_id].invoke({"input": user_message})
    
    return jsonify({
        "success": True,
        "response": response['output'],
        "conversation_id": conversation_id
    })

@app.route('/api/missing-plants', methods=['GET'])
def api_missing_plants():
    """API endpoint to get frequently searched missing plants"""
    # Get query parameters
    limit = request.args.get('limit', 50, type=int)
    min_searches = request.args.get('min_searches', 1, type=int)
    query_type = request.args.get('query_type', None)
    
    # Query missing plants
    missing_plants = get_missing_plants(min_searches, limit, query_type)
    
    # Format response
    results = [{
        'id': plant.id,
        'name': plant.name,
        'scientific_name': plant.scientific_name,
        'search_count': plant.search_count,
        'query_type': plant.query_type,
        'first_searched_at': plant.first_searched_at.isoformat(),
        'last_searched_at': plant.last_searched_at.isoformat()
    } for plant in missing_plants]
    
    return jsonify({
        'count': len(results),
        'results': results
    })