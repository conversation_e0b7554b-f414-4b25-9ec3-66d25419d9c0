# ai/plant_agent.py
import os
from typing import Dict, Any, Optional
from langchain.agents import Agent<PERSON>xecutor, create_openai_tools_agent
from langchain_community.chat_models import ChatOpenAI
from langchain.prompts import Cha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>plate, MessagesPlaceholder
from langchain.tools import BaseTool
from langchain.memory import ConversationBufferMemory
from ai.tools import PlantDatabaseSearch, TrackMissingPlant, ProcessImage, PlantTextQuery
from ai.chains import create_text_query_chain, create_image_identification_chain, process_image_with_llm, parse_llm_response

class PlantAgent:
    """LangChain-based agent for plant identification from text and images"""
    
    def __init__(self, openai_api_key: Optional[str] = None):
        self.api_key = openai_api_key or os.environ.get("OPENAI_API_KEY")
        self.tools = self._setup_tools()
        self.text_query_chain = create_text_query_chain()
        self.image_identification_model = create_image_identification_chain()
    
    def _setup_tools(self) -> list[BaseTool]:
        """Set up the tools for the agent"""
        return [
            PlantDatabaseSearch(),
            TrackMissingPlant(),
            ProcessImage(),
            PlantTextQuery(llm_chain=self.text_query_chain)
        ]
    
    def query_by_name(self, plant_name: str) -> Dict[str, Any]:
        """Query for a plant by name"""
        # First check database
        db_search_tool = PlantDatabaseSearch()
        db_result = db_search_tool._run(plant_name)
        print(f"inside query_by_name: searching for {plant_name}")
        if db_result.get('found'):
            return {
                "success": True,
                "source": "database",
                "result": {
                    "found": True,
                    "name": db_result.get('name'),
                    "scientific_name": db_result.get('scientific_name'),
                    "description": db_result.get('description'),
                    "in_database": True,
                    "database_info": db_result
                }
            }
        print("Not in database")
        # If not in database, use LLM
        llm_response = self.text_query_chain.run(plant_name=plant_name)
        llm_result = parse_llm_response(llm_response, "text")
        
        if not llm_result.get('found'):
            return {
                "success": False,
                "error": "Plant not found",
                "message": llm_result.get('message', 'Unknown plant')
            }
        
        # Track missing plant
        track_tool = TrackMissingPlant()
        track_tool._run(
            name=llm_result.get('name'),
            scientific_name=llm_result.get('scientific_name'),
            query_type="text"
        )
        
        llm_result['in_database'] = False
        
        return {
            "success": True,
            "source": "llm",
            "result": llm_result
        }
    
    def identify_from_image(self, image_path: str) -> Dict[str, Any]:
        """Identify a plant from an image file"""
        # Process the image
        process_tool = ProcessImage()
        image_result = process_tool._run(image_path)
        
        if not image_result.get('success'):
            return {
                "success": False,
                "error": "Failed to process image",
                "details": image_result.get('error')
            }
        
        # Get identification from image
        llm_response = process_image_with_llm(
            self.image_identification_model,
            image_result.get('base64_image')
        )
        
        result = parse_llm_response(llm_response, "image")
        
        if not result.get('found'):
            return {
                "success": False,
                "error": "Could not identify plant from image",
                "details": result.get("error", "Unknown error")
            }
        
        # Check if plant is in database
        db_search_tool = PlantDatabaseSearch()
        db_result = db_search_tool._run(result.get('name'))
        
        if db_result.get('found'):
            result['in_database'] = True
            result['database_info'] = db_result
        else:
            result['in_database'] = False
            # Track missing plant
            track_tool = TrackMissingPlant()
            track_tool._run(
                name=result.get('name'),
                scientific_name=result.get('scientific_name'),
                query_type="image"
            )
        
        return {
            "success": True,
            "result": result
        }
    
    def create_conversational_agent(self) -> AgentExecutor:
        """Create a conversational agent for interactive plant identification"""
        llm = ChatOpenAI(temperature=0, model="gpt-4-turbo")
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", """
            You are PlantPal, an expert plant identification and care assistant. 
            You help users identify plants and provide detailed care information.
            
            Your capabilities include:
            1. Identifying plants by name when users describe or name them
            2. Identifying plants from images when users upload them
            3. Providing detailed care instructions for plants
            4. Answering questions about plant characteristics, growth habits, and problems
            
            When responding to users:
            - If a user asks about a specific plant by name, first check if it's in our database using the plant_database_search tool
            - If the plant isn't in our database, use the plant_text_query tool to get information from external knowledge
            - If a user provides an image, use the process_image tool to prepare it for identification
            - Always track plants that aren't in our database using the track_missing_plant tool
            - Be conversational and friendly, but focus on providing accurate information
            - When discussing care requirements, be specific about light, water, soil, temperature, and humidity needs
            
            Remember that users are often beginners, so explain plant care in simple terms while being thorough.
        """),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad"),
        ])
        
        # Set up memory to maintain conversation history
        memory = ConversationBufferMemory(memory_key="chat_history", return_messages=True)
        
        # Create the agent with tools
        agent = create_openai_tools_agent(llm, self.tools, prompt)
        
        # Create the agent executor
        agent_executor = AgentExecutor(
            agent=agent,
            tools=self.tools,
            memory=memory,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=5,  # Limit the number of tool calls per interaction
            early_stopping_method="generate"  # Stop when the agent decides it has enough information
        )
        
        return agent_executor


